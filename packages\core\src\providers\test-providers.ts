/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { ProviderFactory } from './provider-factory.js';
import { ProviderType } from './types.js';

/**
 * Test function to verify provider system works
 */
export async function testProviders() {
  console.log('🚀 Testing Provider System...\n');

  // Initialize providers
  await ProviderFactory.initializeProviders();

  // List available providers
  const availableProviders = ProviderFactory.getAvailableProviders();
  console.log('📋 Available Providers:', availableProviders);

  // Test OpenAI provider (if API key is available)
  if (process.env.OPENAI_API_KEY) {
    console.log('\n🤖 Testing OpenAI Provider...');
    try {
      const config = ProviderFactory.createProviderConfig(
        ProviderType.OPENAI,
        'gpt-3.5-turbo',
        {
          apiKey: process.env.OPENAI_API_KEY,
        }
      );

      const provider = await ProviderFactory.createProvider(config);
      console.log('✅ OpenAI Provider created successfully');
      console.log('📊 Capabilities:', provider.getCapabilities());
      console.log('🎯 Supported Models:', provider.getSupportedModels().map(m => m.id));

      // Test a simple request
      const response = await provider.generateContent({
        messages: [
          { role: 'user', content: 'Hello! Please respond with just "Hello from OpenAI!"' }
        ],
        maxTokens: 50,
      });

      console.log('💬 Response:', response.content);
      console.log('📈 Usage:', response.usage);
    } catch (error) {
      console.error('❌ OpenAI Provider test failed:', error);
    }
  } else {
    console.log('\n⚠️  OpenAI API key not found, skipping OpenAI test');
  }

  // Test provider detection
  console.log('\n🔍 Testing Provider Detection...');
  const detectedProviders = [
    { model: 'gpt-4', expected: ProviderType.OPENAI },
    { model: 'claude-3-sonnet', expected: ProviderType.ANTHROPIC },
    { model: 'gemini-2.5-pro', expected: ProviderType.GEMINI },
    { model: 'command-r', expected: ProviderType.COHERE },
    { model: 'mistral-large', expected: ProviderType.MISTRAL },
  ];

  for (const { model, expected } of detectedProviders) {
    const detected = ProviderFactory.detectProviderFromModel(model);
    const status = detected === expected ? '✅' : '❌';
    console.log(`${status} ${model} -> ${detected} (expected: ${expected})`);
  }

  // Test recommendations
  console.log('\n💡 Testing Provider Recommendations...');
  const recommendations = [
    { req: { needsLargeContext: true }, desc: 'Large context' },
    { req: { needsImages: true }, desc: 'Image support' },
    { req: { preferFast: true }, desc: 'Fast responses' },
    { req: { preferCheap: true }, desc: 'Cost-effective' },
  ];

  for (const { req, desc } of recommendations) {
    const recommended = ProviderFactory.getRecommendedProvider(req);
    console.log(`🎯 ${desc}: ${recommended}`);
  }

  console.log('\n✨ Provider system test completed!');
}

// Run test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testProviders().catch(console.error);
}
