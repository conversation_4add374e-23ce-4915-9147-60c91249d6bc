/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { AuthType, Config } from '@inkbytefo/s647-cli-core';
import { USER_SETTINGS_PATH } from './config/settings.js';
import { validateAuthMethod } from './config/auth.js';

export async function validateNonInteractiveAuth(
  configuredAuthType: AuthType | undefined,
  nonInteractiveConfig: Config,
) {
  // Check if using new provider system
  const provider = nonInteractiveConfig.getProvider();
  if (provider && provider !== 'gemini') {
    // For new provider system, skip traditional auth validation
    // The provider will handle its own authentication
    console.log(`🔧 Using ${provider} provider - skipping traditional authentication`);

    // Don't call refreshAuth for non-Gemini providers
    // They handle authentication internally via API keys
    return nonInteractiveConfig;
  }

  // Existing Gemini auth logic
  const effectiveAuthType =
    configuredAuthType ||
    (process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true'
      ? AuthType.USE_VERTEX_AI
      : process.env.GEMINI_API_KEY
        ? AuthType.USE_GEMINI
        : undefined);

  if (!effectiveAuthType) {
    console.error(
      `Please set an Auth method in your ${USER_SETTINGS_PATH} or specify either the GEMINI_API_KEY or GOOGLE_GENAI_USE_VERTEXAI environment variables before running`,
    );
    process.exit(1);
  }

  const err = validateAuthMethod(effectiveAuthType);
  if (err != null) {
    console.error(err);
    process.exit(1);
  }

  await nonInteractiveConfig.refreshAuth(effectiveAuthType);
  return nonInteractiveConfig;
}
